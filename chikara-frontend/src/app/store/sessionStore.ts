import { create } from "zustand";
import { createJSONStorage, persist } from "zustand/middleware";

interface Room {
    id: number;
    name: string;
}

interface ChatRoom {
    displayName: string;
    room: Room;
}

// Quest sort and filter types
export type QuestSortOption = "level" | "name" | "quest_giver" | "Yen_reward" | "XP_reward" | "REP_reward";
export type SortDirection = "asc" | "desc";

export type QuestFilterOption = {
    status?: "available" | "in_progress" | "complete" | "ready_to_complete" | "all";
    minLevel?: number;
    maxLevel?: number;
    questGiverId?: number;
    hasItemRewards?: boolean;
    hasTalentPointRewards?: boolean;
    location?: "church" | "shrine" | "mall" | "alley" | "school" | "sewers" | "themepark" | "any";
    isStoryQuest?: boolean;
    chapterId?: number;
};

interface QuestSettings {
    sortOption: QuestSortOption;
    sortDirection: SortDirection;
    filterOptions: QuestFilterOption;
    showFilters: boolean;
}

interface SessionState {
    hideChat: boolean;
    setHideChat: (hideChat: boolean) => void;
    mainChatRoom: ChatRoom;
    setMainChatRoom: (mainChatRoom: ChatRoom) => void;
    inEncounter: boolean;
    setInEncounter: (inEncounter: boolean) => void;
    levelupValue: any | null;
    setLevelupValue: (levelupValue: any) => void;
    hidePollNotification: boolean;
    setHidePollNotification: (hidePollNotification: boolean) => void;
    questSettings: QuestSettings;
    setQuestSettings: (settings: Partial<QuestSettings>) => void;
}

export const sessionStore = create<SessionState>()(
    persist(
        (set) => ({
            hideChat: false,
            setHideChat: (hideChat) => set(() => ({ hideChat })),
            mainChatRoom: { displayName: "Global Chat", room: { id: 1, name: "global" } },
            setMainChatRoom: (mainChatRoom) => set(() => ({ mainChatRoom })),
            inEncounter: false,
            setInEncounter: (inEncounter) => set(() => ({ inEncounter })),
            levelupValue: null,
            setLevelupValue: (levelupValue) => set(() => ({ levelupValue })),
            hidePollNotification: false,
            setHidePollNotification: (hidePollNotification) => set(() => ({ hidePollNotification })),
        }),
        {
            name: "sessionStore",
            storage: createJSONStorage(() => sessionStorage),
            version: 1.1,
        }
    )
);
