# Quest Content Creation Guide for Chikara Academy

## Overview

This guide provides the structure and requirements for creating quest content for the Chikara Academy game. It's designed for LLM use to ensure consistency and proper implementation of quest mechanics.

## Core Quest Structure

### 1. Quest Model (`quest`)

```typescript
interface Quest {
    id: number;
    name: string; // Quest title (max 255 chars)
    description?: string; // Brief quest description (max 255 chars)
    questInfo?: string; // Additional quest information (max 255 chars)
    levelReq: number; // Minimum level required (default: 0)
    disabled?: boolean; // Whether quest is disabled (default: false)
    questChainName?: string; // Name of quest chain if part of one
    xpReward?: number; // XP reward (default: 1)
    cashReward: number; // Cash reward (default: 0)
    repReward: number; // Reputation reward (default: 0.0)
    talentPointReward: number; // Talent points reward (default: 0)
    shopId?: number; // Shop unlock ID (optional)
    requiredQuestId?: number; // Quest prerequisite (optional)

    // Story Quest Properties
    isStoryQuest: boolean; // Whether this is a story quest (default: false)
    chapterId?: number; // Story chapter ID (for story quests)
    orderInChapter?: number; // Order within chapter (for story quests)
}
```

### 2. Quest Objectives (`quest_objective`)

```typescript
interface QuestObjective {
    id: number;
    description?: string; // Objective description (max 255 chars)
    objectiveType: QuestObjectiveTypes; // Type of objective (see enum below)
    target?: number; // Target value (e.g., item ID, creature ID)
    targetAction?: string; // Specific action required
    quantity?: number; // Amount required
    location?: LocationTypes; // Specific location requirement
    isRequired: boolean; // Whether objective is required (default: true)
    questId?: number; // Parent quest ID
    creatureId?: number; // Creature ID for battle objectives
    itemId?: number; // Item ID for item objectives
}
```

### 3. Quest Rewards (`quest_reward`)

```typescript
interface QuestReward {
    id: number;
    rewardType: QuestRewardType; // Type of reward (ITEM, TALENT_POINTS, etc.)
    quantity: number; // Amount of reward
    isChoice: boolean; // Whether this is a choice reward (default: false)
    questId?: number; // Parent quest ID
    itemId?: number; // Item ID for item rewards
}
```

### 4. Quest Progress (`quest_progress`)

```typescript
interface QuestProgress {
    id: number;
    questStatus: QuestProgressStatus; // complete, in_progress, ready_to_complete
    questId?: number; // Quest ID
    userId?: number; // User ID
}
```

### 5. Quest Objective Progress (`quest_objective_progress`)

```typescript
interface QuestObjectiveProgress {
    id: number;
    count: number; // Current progress count (default: 0)
    status: QuestProgressStatus; // Progress status
    userId: number; // User ID
    questObjectiveId: number; // Objective ID
}
```

## Quest Objective Types

### Battle Objectives

- `DEFEAT_NPC` - Defeat specific NPC (target=creatureId) or any NPC (target=null)
- `DEFEAT_NPC_IN_TURNS` - Defeat a specific NPC within a turn limit
- `DEFEAT_NPC_WITH_LOW_DAMAGE` - Defeat any NPC while taking minimal damage
- `DEFEAT_BOSS` - Defeat N boss NPCs
- `DEFEAT_PLAYER` - Defeat N players
- `PVP_POST_BATTLE_CHOICE` - PVP battle won with specific post-battle choice
- `DEFEAT_PLAYER_XNAME` - Defeat player with x in name x times
- `DEFEAT_SPECIFIC_PLAYER` - Defeat specific player x times
- `WIN_BATTLE` - Win N battles of any type (PvE or PvP)
- `USE_ABILITY` - Use an ability N times

### Item Objectives

- `ACQUIRE_ITEM` - Obtain N of item (from loot, shops, etc.)
- `CRAFT_ITEM` - Craft specific item (target=itemId) or any item (target=null)
- `DELIVER_ITEM` - Hand in N items to a quest giver or location
- `GATHER_RESOURCES` - Gather N resources through mining, scavenging, or foraging

### Bounty Objectives

- `PLACE_BOUNTY` - Place N bounties on other players
- `COLLECT_BOUNTY_REWARD` - Collect the reward for N bounties

### Miscellaneous Objectives

- `COMPLETE_MISSIONS` - Complete N missions
- `DONATE_TO_SHRINE` - Donate N amount to the shrine
- `VOTE_ON_SUGGESTION` - Cast a vote in N community suggestions
- `CHARACTER_ENCOUNTERS` - Complete N character encounters
- `TRAIN_STATS` - Train N stats
- `GAMBLING_SLOTS` - Gamble N amount on slots

### Story Objectives

- `COMPLETE_STORY_EPISODE` - Complete a story episode
- `UNIQUE_OBJECTIVE` - Custom objective for unique quest mechanics

## Quest Reward Types

- `ITEM` - Item rewards (requires itemId)
- `TALENT_POINTS` - Talent point rewards
- `GANG_CREDS` - Gang credential rewards
- `CLASS_POINTS` - Class point rewards

## Locations

Available locations for quest objectives:

- `church`
- `shrine`
- `mall`
- `alley`
- `school`
- `sewers`
- `themepark`
- `shibuya`
- `shinjuku`
- `bunkyo`
- `chiyoda`
- `minato`
- `any`

## Quest Creation Template

### Basic Quest Template

```typescript
const questTemplate = {
    // Basic Info
    name: "Quest Name",
    description: "Brief description of the quest",
    questInfo: "Additional information about the quest",
    levelReq: 1,

    // Rewards
    xpReward: 100,
    cashReward: 500,
    repReward: 0.5,
    talentPointReward: 0,

    // Chain Info (if applicable)
    questChainName: "Chain Name",
    requiredQuestId: null,

    // Story Quest Info (if applicable)
    isStoryQuest: false,
    chapterId: null,
    orderInChapter: null,

    // Objectives
    objectives: [
        {
            description: "Objective description",
            objectiveType: "DEFEAT_NPC",
            target: null,
            targetAction: null,
            quantity: 3,
            location: "any",
            isRequired: true,
            creatureId: null,
            itemId: null,
        },
    ],

    // Additional Rewards
    rewards: [
        {
            rewardType: "ITEM",
            quantity: 1,
            isChoice: false,
            itemId: 123,
        },
    ],
};
```

### Story Quest Template

```typescript
const storyQuestTemplate = {
    name: "Story Quest Name",
    description: "Story quest description",
    questInfo: "Story context information",
    levelReq: 1,

    // Story Quest Specific
    isStoryQuest: true,
    chapterId: 1,
    orderInChapter: 1,

    // Rewards
    xpReward: 200,
    cashReward: 1000,
    repReward: 1.0,
    talentPointReward: 1,

    // Story objectives typically use COMPLETE_STORY_EPISODE
    objectives: [
        {
            description: "Complete the story episode",
            objectiveType: "COMPLETE_STORY_EPISODE",
            target: null,
            quantity: 1,
            location: "shibuya",
            isRequired: true,
        },
    ],

    rewards: [
        {
            rewardType: "TALENT_POINTS",
            quantity: 1,
            isChoice: false,
        },
    ],
};
```

## Quest Creation Guidelines

### 1. Naming Conventions

- Use descriptive, immersive names
- Keep descriptions concise but informative
- Follow the game's cyberpunk/academy theme

### 2. Level Requirements

- Set appropriate level requirements based on difficulty
- Consider prerequisites from previous quests
- Balance challenge with accessibility

### 3. Rewards

- Scale rewards appropriately with difficulty and level
- Consider both immediate and long-term progression
- Balance different reward types

### 4. Objectives

- Use clear, specific objective descriptions
- Set realistic quantity requirements
- Consider player engagement and variety

### 5. Quest Chains

- Use consistent naming for quest chains
- Ensure proper prerequisite relationships
- Create logical story progression

## Story Quest Integration

### Story Structure

- **Season** → **Chapter** → **Quest** → **Objective** → **Episode**
- Each story quest should have `isStoryQuest: true`
- Must specify `chapterId` and `orderInChapter`

### Story Episode Content

Story episodes contain:

- **Narrative content** (dialogue, scenes)
- **Choices** (for CHOICE type episodes)
- **Battle scenarios** (for BATTLE type episodes)

## Best Practices

### 1. Quest Design

- Create varied objective types to maintain interest
- Balance difficulty progression
- Ensure clear objective descriptions
- Consider player skill levels and equipment

### 2. Reward Balance

- Match rewards to effort required
- Consider economic impact of cash rewards
- Balance XP gains with level progression

### 3. Story Integration

- Ensure story quests flow logically
- Maintain consistent tone and theme
- Consider character development

### 4. Technical Considerations

- Validate all referenced IDs (items, creatures, etc.)
- Ensure proper quest chain dependencies
- Test objective completion conditions

## Common Patterns

### Tutorial Quest

```typescript
{
  name: "Academy Orientation",
  description: "Complete your first day at Chikara Academy",
  levelReq: 1,
  xpReward: 50,
  cashReward: 100,
  objectives: [
    {
      description: "Talk to the Academy Guide",
      objectiveType: "CHARACTER_ENCOUNTERS",
      quantity: 1,
      location: "school"
    }
  ]
}
```

### Combat Quest

```typescript
{
  name: "Street Patrol",
  description: "Defeat thugs in the school district",
  levelReq: 3,
  xpReward: 150,
  cashReward: 300,
  objectives: [
    {
      description: "Defeat 5 street thugs",
      objectiveType: "DEFEAT_NPC",
      quantity: 5,
      location: "school"
    }
  ]
}
```

### Collection Quest

```typescript
{
  name: "Resource Gathering",
  description: "Collect materials for crafting",
  levelReq: 5,
  xpReward: 100,
  cashReward: 200,
  objectives: [
    {
      description: "Gather 10 scrap metal",
      objectiveType: "GATHER_RESOURCES",
      targetAction: "scavenging",
      quantity: 10,
      location: "sewers"
    }
  ]
}
```

This guide provides the foundation for creating engaging, properly structured quest content that integrates seamlessly with the Chikara Academy game systems.
