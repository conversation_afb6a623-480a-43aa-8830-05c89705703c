# Chikara Academy Frontend: Rules & Overview

## Main Tech Stack

- **Runtime:** Bun (Node.js compatible)
- **Language:** TypeScript
- **Framework:** React 19+ with JSX
- **Build Tool:** Vite 7+ with React plugin
- **Styling:** Tailwind CSS 4+ with Radix UI components
- **State Management:** Zustand with React Tracked
- **Data Fetching:** TanStack Query with oRPC client
- **Routing:** React Router DOM 7+
- **Real-time:** Socket.io client
- **Testing:** Vitest + Playwright for E2E
- **PWA:** Vite PWA plugin with service worker

## Project Structure (Feature-Based)

```
chikara-frontend/
├── public/ # Static assets and PWA manifest
├── service-worker/ # Firebase messaging service worker
├── src/
│ ├── app/ # App-level configuration and providers
│ ├── assets/ # Static assets (images, fonts, sounds)
│ ├── components/ # Reusable UI components
│ ├── constants/ # App constants and configuration
│ ├── features/ # Feature modules (See below)
│ ├── helpers/ # Utility functions and helpers
│ ├── hooks/ # Custom React hooks
│ ├── lib/ # Core libraries and configurations
│ ├── pages/ # Route components
│ ├── test/ # Test utilities and setup
│ ├── types/ # TypeScript type definitions
│ ├── utils/ # Pure utility functions
│ ├── App.tsx # Main app component
│ ├── index.tsx # App entry point
│ └── SocketManager.tsx # Socket.io connection manager
└── playwright/ # E2E tests
```

## Feature Organization (`src/features/`)

```
feature-name/
├── __tests__/ # Vitest unit tests
├── components/ # Feature-specific components
├── hooks/ # Feature-specific hooks
├── types/ # Feature-specific types
├── utils/ # Feature-specific utilities
├── index.ts # Feature exports
└── FeaturePage.tsx # Main feature page component
```

### Feature Guidelines:

1. **Single Responsibility:** Each feature handles one domain area
2. **Self-Contained:** Features should not directly import from other features
3. **Shared Logic:** Use `src/lib/`, `src/helpers/`, or `src/hooks/` for cross-feature functionality
4. **Testing:** Unit tests in `__tests__` alongside the feature
5. **Exports:** Clean public API through `index.ts`

## State Management (Zustand + React Tracked)

### Store Types:

- **persistStore:** Persistent data across sessions (localStorage)
- **sessionStore:** Session-only data (sessionStorage)
- **normalStore:** In-memory state
- **socketStore:** Real-time socket state
- **authStore:** Authentication state (persistent)

### Store Guidelines:

- Use `createTrackedSelector` for automatic re-render optimization
- Keep stores focused on specific domains
- Use persistent stores sparingly (performance impact)
- Prefer React Query for server state over Zustand

## API Integration (oRPC + TanStack Query)

### oRPC Client Setup:

- **Main API:** `client` from `src/lib/orpc.ts`
- **Admin API:** `adminClient` for admin operations
- **Type Safety:** End-to-end TypeScript from backend to frontend
- **Authentication:** Automatic cookie-based auth with error handling
- **CSRF Protection:** Built-in CSRF protection plugin

### Query Patterns:

```typescript
// Using oRPC with TanStack Query
const { data, isLoading, error } = orpc.user.getProfile.useQuery({
    input: { userId: 123 },
    staleTime: 30000,
});

// Mutations
const mutation = orpc.user.updateProfile.useMutation({
    onSuccess: () => {
        // Invalidate related queries
        queryClient.invalidateQueries(["user"]);
    },
});
```

### API Guidelines:

- Use oRPC utilities (`orpc.*`) for type-safe queries
- Handle loading states consistently
- Implement proper error boundaries
- Use optimistic updates for better UX
- Leverage query invalidation for data consistency

## Component Architecture

### Component Types:

1. **Pages:** Route-level components in `src/pages/`
2. **Features:** Domain-specific components in `src/features/`
3. **UI Components:** Reusable components in `src/components/`
4. **Layout Components:** App structure in `src/components/Layout/`

### Component Guidelines:

- Use functional components with hooks
- Implement proper TypeScript interfaces
- Follow React 19 best practices
- Use React Compiler optimizations
- Implement proper error boundaries

## Styling (Tailwind CSS + Radix UI)

### Styling Approach:

- **Utility-First:** Tailwind CSS for styling
- **Component Library:** Radix UI for accessible primitives
- **Custom Components:** shadcn/ui pattern for complex components
- **Responsive Design:** Mobile-first approach
- **Dark Mode:** Built-in theme support

### Styling Guidelines:

- Use Tailwind utilities over custom CSS
- Leverage Radix UI for accessibility
- Implement consistent spacing and typography
- Use CSS variables for theme customization
- Follow mobile-first responsive design

## Real-time Features (Socket.io)

### Socket Management:

- **Connection:** Managed by `SocketManager.tsx`
- **State:** Stored in `socketStore`
- **Events:** Feature-specific socket handlers
- **Reconnection:** Automatic reconnection handling

### Socket Guidelines:

- Use socket store for connection state
- Implement proper event cleanup
- Handle connection errors gracefully
- Use typed socket events
- Implement proper authentication

## Testing Strategy

### Testing Types:

1. **Unit Tests:** Vitest for component and utility testing
2. **Integration Tests:** Feature-level testing with React Testing Library
3. **E2E Tests:** Playwright for full user journey testing
4. **Visual Tests:** Playwright for visual regression testing

### Testing Guidelines:

- Test user interactions, not implementation details
- Use proper test utilities from `src/test/`
- Mock external dependencies appropriately
- Implement proper test cleanup
- Use descriptive test names

## Code Style (TypeScript + React)

### TypeScript Guidelines:

- **Strict Mode:** Full TypeScript strict mode enabled
- **Type Safety:** Prefer explicit types over `any`
- **Interfaces:** Use interfaces for object shapes
- **Generics:** Leverage generics for reusable components
- **Type Guards:** Implement proper type guards

### React Guidelines:

- **Hooks:** Use hooks for state and side effects
- **Custom Hooks:** Extract reusable logic into custom hooks
- **Error Boundaries:** Implement error boundaries for robustness
- **Performance:** Use React.memo and useMemo judiciously
- **Accessibility:** Follow WCAG guidelines

### Import Guidelines:

- **Absolute Imports:** Use `@/` alias for src imports
- **Import Order:** External libraries, internal modules, relative imports
- **Named Exports:** Prefer named exports over default exports
- **Tree Shaking:** Structure imports for optimal bundling

## Performance Optimization

### Build Optimization:

- **Code Splitting:** Route-based code splitting
- **Bundle Analysis:** Regular bundle size monitoring
- **Asset Optimization:** Image and asset optimization
- **Caching:** Proper browser caching strategies

### Runtime Optimization:

- **React Compiler:** Automatic optimization with React Compiler
- **Query Optimization:** Proper query caching and invalidation
- **State Optimization:** Minimal re-renders with React Tracked
- **Lazy Loading:** Component and route lazy loading

## PWA Features

### Service Worker:

- **Caching:** Offline-first caching strategy
- **Push Notifications:** Firebase messaging integration
- **Background Sync:** Background data synchronization
- **App Updates:** Automatic app update handling

### PWA Guidelines:

- Implement proper offline fallbacks
- Handle network connectivity changes
- Provide meaningful offline experiences
- Use proper caching strategies

## Development Workflow

### Commands:

```bash
# Development
bun dev              # Start development server
bun build            # Build for production
bun test             # Run unit tests
bun test:watch       # Run tests in watch mode
bun test:visual      # Run E2E tests
bun lint             # Run ESLint
bun format           # Run Prettier
bun type-check       # TypeScript checking
```

### Development Guidelines:

- Use TypeScript for all new code
- Follow ESLint and Prettier configurations
- Write tests for new features
- Use proper Git commit messages
- Implement proper error handling

## Error Handling

### Error Types:

- **API Errors:** Handled by query client with global error handling
- **Component Errors:** Caught by error boundaries
- **Authentication Errors:** Automatic logout and redirect
- **Network Errors:** Proper offline handling

### Error Guidelines:

- Implement proper error boundaries
- Use toast notifications for user feedback
- Log errors appropriately for debugging
- Provide meaningful error messages
- Handle edge cases gracefully

## Security Considerations

### Security Measures:

- **CSRF Protection:** Built-in CSRF protection with oRPC
- **Authentication:** Secure cookie-based authentication
- **Input Validation:** Client-side validation with server verification
- **XSS Prevention:** Proper data sanitization
- **Content Security Policy:** Implemented via headers

### Security Guidelines:

- Never store sensitive data in localStorage
- Validate all user inputs
- Use HTTPS in production
- Implement proper authentication flows
- Follow security best practices

## Deployment & Build

### Build Configuration:

- **Vite:** Optimized production builds
- **Source Maps:** Disabled for production
- **Asset Optimization:** Automatic asset optimization
- **PWA:** Service worker generation

### Deployment Guidelines:

- Use environment variables for configuration
- Implement proper CI/CD pipelines
- Monitor bundle sizes
- Use proper caching headers
- Implement health checks

## Key Dependencies

### Core Libraries:

- **React 19+:** Latest React with concurrent features
- **oRPC:** Type-safe RPC communication
- **TanStack Query:** Server state management
- **Zustand:** Client state management
- **React Router:** Client-side routing
- **Tailwind CSS:** Utility-first styling
- **Radix UI:** Accessible component primitives
- **Socket.io:** Real-time communication
- **Better Auth:** Authentication system

### Development Tools:

- **Vite:** Fast build tool and dev server
- **Vitest:** Fast unit testing framework
- **Playwright:** E2E testing framework
- **ESLint:** Code linting
- **Prettier:** Code formatting
- **TypeScript:** Type safety

## Usage Guidelines

### General Principles:

- **Type Safety:** Leverage TypeScript for better developer experience
- **Performance:** Optimize for mobile and slow networks
- **Accessibility:** Follow WCAG guidelines for inclusive design
- **User Experience:** Prioritize smooth, responsive interactions
- **Maintainability:** Write clean, well-documented code

### Best Practices:

- Use proper loading states and error handling
- Implement optimistic updates for better UX
- Follow React best practices and patterns
- Use proper semantic HTML
- Implement proper keyboard navigation
- Test across different devices and browsers
